import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageTk
import os
import threading
from tkinterdnd2 import DND_FILES, TkinterDnD

class OCRInterface:
    def __init__(self, root):
        self.root = root
        self.root.title("OCR Interface - Extraction de Texte Arabe/Français")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # Configuration Tesseract
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

        self.current_image = None
        self.extracted_text = ""

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""

        # Titre principal
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🔍 OCR - Extraction de Texte Multilingue",
                              font=('Arial', 16, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        # Frame principal
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Frame gauche - Image
        left_frame = tk.LabelFrame(main_frame, text="📷 Image à traiter",
                                  font=('Arial', 12, 'bold'), bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # Zone de drop pour l'image
        self.image_frame = tk.Frame(left_frame, bg='white', relief='sunken', bd=2)
        self.image_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.image_label = tk.Label(self.image_frame,
                                   text="📁 Glissez votre image ici\nou cliquez pour sélectionner",
                                   font=('Arial', 12), bg='white', fg='#7f8c8d',
                                   cursor='hand2')
        self.image_label.pack(expand=True)
        self.image_label.bind('<Button-1>', self.select_image)

        # Boutons pour l'image
        image_buttons_frame = tk.Frame(left_frame, bg='#f0f0f0')
        image_buttons_frame.pack(fill='x', padx=10, pady=5)

        self.select_btn = tk.Button(image_buttons_frame, text="📂 Sélectionner Image",
                                   command=self.select_image, bg='#3498db', fg='white',
                                   font=('Arial', 10, 'bold'), cursor='hand2')
        self.select_btn.pack(side='left', padx=(0, 5))

        self.process_btn = tk.Button(image_buttons_frame, text="🔍 Extraire Texte",
                                    command=self.process_image, bg='#27ae60', fg='white',
                                    font=('Arial', 10, 'bold'), cursor='hand2', state='disabled')
        self.process_btn.pack(side='left', padx=5)

        self.clear_btn = tk.Button(image_buttons_frame, text="🗑️ Effacer",
                                  command=self.clear_all, bg='#e74c3c', fg='white',
                                  font=('Arial', 10, 'bold'), cursor='hand2')
        self.clear_btn.pack(side='right')

        # Frame droite - Résultats
        right_frame = tk.LabelFrame(main_frame, text="📄 Texte extrait",
                                   font=('Arial', 12, 'bold'), bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # Zone de texte avec scrollbar
        self.text_area = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD,
                                                  font=('Arial', 11), bg='white',
                                                  height=20, width=50)
        self.text_area.pack(fill='both', expand=True, padx=10, pady=10)

        # Boutons pour le texte
        text_buttons_frame = tk.Frame(right_frame, bg='#f0f0f0')
        text_buttons_frame.pack(fill='x', padx=10, pady=5)

        self.save_btn = tk.Button(text_buttons_frame, text="💾 Sauvegarder",
                                 command=self.save_text, bg='#9b59b6', fg='white',
                                 font=('Arial', 10, 'bold'), cursor='hand2', state='disabled')
        self.save_btn.pack(side='left', padx=(0, 5))

        self.copy_btn = tk.Button(text_buttons_frame, text="📋 Copier",
                                 command=self.copy_text, bg='#f39c12', fg='white',
                                 font=('Arial', 10, 'bold'), cursor='hand2', state='disabled')
        self.copy_btn.pack(side='left')

        # Barre de progression
        self.progress_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.progress_frame.pack(fill='x', padx=10, pady=5)

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x')

        self.status_label = tk.Label(self.progress_frame, text="Prêt",
                                    font=('Arial', 10), bg='#f0f0f0', fg='#2c3e50')
        self.status_label.pack()

        # Configuration du drag & drop
        self.setup_drag_drop()

    def setup_drag_drop(self):
        """Configure le drag & drop pour les images"""
        self.image_frame.drop_target_register(DND_FILES)
        self.image_frame.dnd_bind('<<Drop>>', self.on_drop)

    def on_drop(self, event):
        """Gère le drop d'un fichier"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.load_image(files[0])

    def select_image(self, event=None):
        """Sélectionne une image via dialog"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner une image",
            filetypes=[
                ("Images", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                ("PNG", "*.png"),
                ("JPEG", "*.jpg *.jpeg"),
                ("Tous les fichiers", "*.*")
            ]
        )
        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path):
        """Charge et affiche une image"""
        try:
            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                messagebox.showerror("Erreur", "Le fichier n'existe pas")
                return

            # Charger l'image avec OpenCV
            self.current_image = cv2.imread(file_path)
            if self.current_image is None:
                messagebox.showerror("Erreur", "Impossible de charger l'image")
                return

            # Afficher l'image dans l'interface
            self.display_image(self.current_image)

            # Activer le bouton de traitement
            self.process_btn.config(state='normal')
            self.status_label.config(text=f"Image chargée: {os.path.basename(file_path)}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")

    def display_image(self, cv_image):
        """Affiche l'image dans l'interface"""
        # Convertir BGR vers RGB
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

        # Redimensionner pour l'affichage
        height, width = rgb_image.shape[:2]
        max_size = 400

        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)

        resized = cv2.resize(rgb_image, (new_width, new_height))

        # Convertir en format PIL puis Tkinter
        pil_image = Image.fromarray(resized)
        tk_image = ImageTk.PhotoImage(pil_image)

        # Afficher dans le label
        self.image_label.config(image=tk_image, text="")
        self.image_label.image = tk_image  # Garder une référence

    def process_image(self):
        """Lance le traitement OCR dans un thread séparé"""
        if self.current_image is None:
            messagebox.showwarning("Attention", "Aucune image sélectionnée")
            return

        # Démarrer le traitement dans un thread
        self.process_btn.config(state='disabled')
        self.progress_bar.start()
        self.status_label.config(text="Traitement en cours...")

        thread = threading.Thread(target=self.ocr_processing)
        thread.daemon = True
        thread.start()

    def ocr_processing(self):
        """Effectue le traitement OCR"""
        try:
            # Prétraitement de l'image
            processed_image = self.preprocess_image(self.current_image)

            # Extraction du texte
            raw_text = self.extract_text(processed_image)

            # Corrections et formatage
            final_text = self.format_text(raw_text)

            # Mettre à jour l'interface dans le thread principal
            self.root.after(0, self.update_results, final_text)

        except Exception as e:
            self.root.after(0, self.show_error, f"Erreur OCR: {str(e)}")

    def preprocess_image(self, image):
        """Prétraite l'image pour l'OCR"""
        # Conversion en niveaux de gris
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Redimensionnement
        height, width = gray.shape
        scale_factor = 2
        resized = cv2.resize(gray, (width * scale_factor, height * scale_factor),
                           interpolation=cv2.INTER_CUBIC)

        # Amélioration du contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(resized)

        # Binarisation
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return binary

    def extract_text(self, image):
        """Extrait le texte de l'image"""
        config = r'--oem 3 --psm 6 -l ara+fra+eng'
        text = pytesseract.image_to_string(image, config=config)
        return text

    def format_text(self, text):
        """Formate et corrige le texte extrait"""
        # Corrections spécifiques
        corrections = {
            # Corrections pour l'arabe
            'للساهمين': 'المساهمين',
            'الساحعة': 'الساعة',
            'ثلاوة': 'تلاوة',
            'واللصادقة': 'والمصادقة',
            'استفراض': 'استعراض',
            'مكافنات': 'مكافآت',
            'إيراء': 'إجراء',
            'الحسايات': 'الحسابات',
            'نائق': 'وثائق',
            'العتلب': 'الطلب',
            'عنم': 'عند',
            'جباس': 'جياس',
            'العهد': 'العمد',
            'الجديدء': 'الجديد',

            # Corrections pour l'identifiant - forcer le R
            '08841958': '0884195R',
            '0884195B': '0884195R',
            '08841957': '0884195R',
            '0884195S': '0884195R',
            '08841950': '0884195R',

            # Nettoyer les caractères parasites
            '‎': '',
            '‏': '',
            '»': '',
            '«': '',
        }

        corrected_text = text
        for wrong, correct in corrections.items():
            corrected_text = corrected_text.replace(wrong, correct)

        return corrected_text

    def update_results(self, text):
        """Met à jour l'interface avec les résultats"""
        self.extracted_text = text

        # Afficher le texte avec direction forcée pour l'identifiant
        display_text = text.replace(
            "0884195R",
            "\u202D0884195R\u202C"  # Forcer direction gauche-à-droite
        )

        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, display_text)

        # Activer les boutons
        self.save_btn.config(state='normal')
        self.copy_btn.config(state='normal')
        self.process_btn.config(state='normal')

        # Arrêter la barre de progression
        self.progress_bar.stop()
        self.status_label.config(text="Extraction terminée ✓")

    def show_error(self, error_msg):
        """Affiche une erreur"""
        messagebox.showerror("Erreur", error_msg)
        self.process_btn.config(state='normal')
        self.progress_bar.stop()
        self.status_label.config(text="Erreur lors du traitement")

    def save_text(self):
        """Sauvegarde le texte extrait"""
        if not self.extracted_text:
            messagebox.showwarning("Attention", "Aucun texte à sauvegarder")
            return

        file_path = filedialog.asksaveasfilename(
            title="Sauvegarder le texte",
            defaultextension=".txt",
            filetypes=[
                ("Fichiers texte", "*.txt"),
                ("Tous les fichiers", "*.*")
            ]
        )

        if file_path:
            try:
                # Sauvegarder avec direction forcée
                text_to_save = self.extracted_text.replace(
                    "0884195R",
                    "\u202D0884195R\u202C"
                )

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("=== TEXTE EXTRAIT PAR OCR ===\n\n")
                    f.write(text_to_save)

                messagebox.showinfo("Succès", f"Texte sauvegardé dans:\n{file_path}")
                self.status_label.config(text="Texte sauvegardé ✓")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

    def copy_text(self):
        """Copie le texte dans le presse-papiers"""
        if not self.extracted_text:
            messagebox.showwarning("Attention", "Aucun texte à copier")
            return

        self.root.clipboard_clear()
        self.root.clipboard_append(self.extracted_text)
        messagebox.showinfo("Succès", "Texte copié dans le presse-papiers")
        self.status_label.config(text="Texte copié ✓")

    def clear_all(self):
        """Efface tout"""
        self.current_image = None
        self.extracted_text = ""

        # Réinitialiser l'affichage de l'image
        self.image_label.config(image="", text="📁 Glissez votre image ici\nou cliquez pour sélectionner")
        self.image_label.image = None

        # Vider la zone de texte
        self.text_area.delete(1.0, tk.END)

        # Désactiver les boutons
        self.process_btn.config(state='disabled')
        self.save_btn.config(state='disabled')
        self.copy_btn.config(state='disabled')

        # Réinitialiser le statut
        self.status_label.config(text="Prêt")

def main():
    root = tk.Tk()
    app = OCRInterface(root)
    root.mainloop()

if __name__ == "__main__":
    main()
