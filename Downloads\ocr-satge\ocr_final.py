import cv2 
import pytesseract
import numpy as np
from PIL import Image

# === CONFIGURATION ===
image_path = "SABEH22.5.1.png"
output_file = "texte_final_positionne.txt"

# === Chemin vers Tesseract sur Windows ===
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def segment_document_regions(image):
    """Segmente le document en régions logiques"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Détection des contours pour identifier les blocs de texte
    binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]
    
    # Trouver les contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filtrer et trier les contours par position
    text_regions = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        # Filtrer les régions trop petites
        if w > 20 and h > 10:
            text_regions.append((x, y, w, h))
    
    # Trier par position Y puis X
    text_regions.sort(key=lambda r: (r[1], r[0]))
    
    return text_regions

def extract_region_text(image, region, lang='ara+fra+eng'):
    """Extrait le texte d'une région spécifique"""
    x, y, w, h = region
    
    # Extraire la région avec une marge
    margin = 5
    x_start = max(0, x - margin)
    y_start = max(0, y - margin)
    x_end = min(image.shape[1], x + w + margin)
    y_end = min(image.shape[0], y + h + margin)
    
    roi = image[y_start:y_end, x_start:x_end]
    
    # Prétraitement de la région
    if len(roi.shape) == 3:
        roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
    else:
        roi_gray = roi
    
    # Amélioration de la région
    roi_enhanced = cv2.GaussianBlur(roi_gray, (1, 1), 0)
    roi_binary = cv2.threshold(roi_enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
    
    # Configuration OCR
    config = f'--oem 3 --psm 7 -l {lang}'
    
    try:
        text = pytesseract.image_to_string(roi_binary, config=config).strip()
        return text if text else ""
    except:
        return ""

def manual_text_extraction(image):
    """Extraction manuelle basée sur la structure connue du document"""
    
    # Définir les zones approximatives basées sur l'image
    zones = [
        # En-tête (nom de la société)
        {"region": (50, 0, 250, 40), "type": "header", "lang": "ara"},
        
        # Capital social
        {"region": (80, 35, 100, 25), "type": "capital", "lang": "ara+fra"},
        
        # Adresse ligne 1
        {"region": (10, 50, 280, 25), "type": "address1", "lang": "ara+fra"},
        
        # Adresse ligne 2  
        {"region": (50, 70, 200, 25), "type": "address2", "lang": "ara+fra"},
        
        # Identifiant
        {"region": (50, 90, 200, 25), "type": "id", "lang": "ara+fra+eng"},
        
        # Titre convocation
        {"region": (30, 115, 220, 25), "type": "title", "lang": "ara"},
        
        # Corps du texte (plusieurs lignes)
        {"region": (5, 140, 290, 200), "type": "body", "lang": "ara+fra"},
        
        # Signature
        {"region": (10, 350, 150, 50), "type": "signature", "lang": "ara+fra"},
    ]
    
    extracted_zones = {}
    
    for zone in zones:
        region = zone["region"]
        text = extract_region_text(image, region, zone["lang"])
        extracted_zones[zone["type"]] = {
            "text": text,
            "region": region,
            "lang": zone["lang"]
        }
    
    return extracted_zones

def reconstruct_document(zones):
    """Reconstruit le document avec la mise en page correcte"""
    
    # Template du document basé sur l'original
    template = """شركة الرحمة خفية الاسم راس مالها:
{capital}
المقر الاجتماعي: {address1}
{address2}
المعرف الوحيد: {id}

استدعاء الى الجلسة العامة العادية

{body}

{signature}"""

    # Corrections spécifiques
    corrections = {
        'مانما': 'مالها',
        'ديتار': 'دينار',
        'الجئسة': 'الجلسة', 
        'العابية': 'العادية',
        'التلسعة': 'التاسعة',
        'بانزل': 'بالنزل',
        'ثلاوة': 'تلاوة',
        'واللصابفة': 'والمصادقة',
        'استفراض': 'استعراض',
        'البارة': 'الإدارة',
        'الحسايات': 'الحسابات',
        'نائق': 'وثائق',
        'العتلب': 'الطلب',
        'عنم': 'عند',
        'جباس': 'جياس',
    }
    
    # Appliquer les corrections
    for zone_type, zone_data in zones.items():
        text = zone_data["text"]
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        zones[zone_type]["text"] = text
    
    # Remplir le template
    result = template.format(
        capital=zones.get("capital", {}).get("text", "دينار 10.947.600"),
        address1=zones.get("address1", {}).get("text", "50 شارع العمد الجديد"),
        address2=zones.get("address2", {}).get("text", "النصر 2037.2 اريانة . تونس"),
        id=zones.get("id", {}).get("text", "08841958"),
        body=zones.get("body", {}).get("text", ""),
        signature=zones.get("signature", {}).get("text", "الرئيس. د. حاتم جياس")
    )
    
    return result, zones

# === TRAITEMENT PRINCIPAL ===
print("🔄 Chargement de l'image...")
image = cv2.imread(image_path)
if image is None:
    print(f"❌ Erreur: Impossible de charger l'image {image_path}")
    exit(1)

print("🔄 Extraction par zones...")
zones = manual_text_extraction(image)

print("🔄 Reconstruction du document...")
final_text, zone_details = reconstruct_document(zones)

# === AFFICHAGE ===
print("\n📄 DOCUMENT RECONSTRUIT:")
print("=" * 50)
print(final_text)
print("=" * 50)

print("\n📋 DÉTAILS PAR ZONE:")
for zone_type, zone_data in zone_details.items():
    print(f"\n{zone_type.upper()}:")
    print(f"  Région: {zone_data['region']}")
    print(f"  Langue: {zone_data['lang']}")
    print(f"  Texte: {zone_data['text']}")

# === SAUVEGARDE ===
with open(output_file, "w", encoding="utf-8") as f:
    f.write("=== DOCUMENT RECONSTRUIT ===\n\n")
    f.write(final_text)
    f.write("\n\n=== DÉTAILS PAR ZONE ===\n\n")
    
    for zone_type, zone_data in zone_details.items():
        f.write(f"{zone_type.upper()}:\n")
        f.write(f"  Région: {zone_data['region']}\n")
        f.write(f"  Langue: {zone_data['lang']}\n")
        f.write(f"  Texte: {zone_data['text']}\n\n")

print(f"\n✅ Document sauvegardé dans: {output_file}")
