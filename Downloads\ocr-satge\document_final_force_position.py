import codecs

def create_document_with_forced_positioning():
    """Crée un document avec positionnement forcé du R"""
    
    # Document final avec caractères de contrôle pour forcer la position
    document_content = """شركة الرحمة خفية الاسم راس مالها:
دينار 10.947.600

المقر الاجتماعي: 50 شارع العمد الجديد
النصر 2037.2 اريانة . تونس
المعرف الوحيد: 0884195R

استدعاء الى الجلسة العامة العادية

ان السيدات والسادة المساهمين مدعوون لحضور الجلسة
العامة العادية التي ستنعقد يوم السبت 21 جوان 2025
على الساعة التاسعة صباحا ب النزل ذو بنتهاوس —
النصر

للنظر في جدول الأعمال التالي:
1- تلاوة والمصادقة على تقرير مجلس الإدارة حول نشاط
   سنة 2024
2- تلاوة تقريري مراقب الحسابات لسنة 2024
3- استعراض والمصادقة على القائمات المالية لسنة 2024
   وتخصيص النتائج
4- تحديد مبلغ مكافآت الحضور لأعضاء مجلس الإدارة
5- إجراء دورة أعضاء مجلس الإدارة
6- تعيين مراقب الحسابات
7- مسائل أخرى

الوثائق التحضيرية للاجتماع متاحة عند الطلب من
المساهمين بالشركة بالمقر الاجتماعي قبل 15 يوما قبل
الجلسة العامة.

عن مجلس الإدارة
الرئيس.د. حاتم جياس
PT250308"""

    return document_content

def save_with_multiple_encodings(content):
    """Sauvegarde avec différents encodages pour assurer la compatibilité"""
    
    # Version UTF-8 standard
    with open("document_utf8.txt", "w", encoding="utf-8") as f:
        f.write("=== DOCUMENT FINAL UTF-8 ===\n\n")
        f.write(content)
    
    # Version UTF-8 avec BOM
    with open("document_utf8_bom.txt", "w", encoding="utf-8-sig") as f:
        f.write("=== DOCUMENT FINAL UTF-8 BOM ===\n\n")
        f.write(content)
    
    # Version avec caractères de contrôle pour forcer la direction
    content_with_controls = content.replace(
        "المعرف الوحيد: 0884195R",
        "المعرف الوحيد: \u202D0884195R\u202C"  # LTR override pour forcer la direction
    )
    
    with open("document_force_direction.txt", "w", encoding="utf-8") as f:
        f.write("=== DOCUMENT AVEC DIRECTION FORCÉE ===\n\n")
        f.write(content_with_controls)
    
    # Version avec espaces insécables
    content_with_spaces = content.replace(
        "المعرف الوحيد: 0884195R",
        "المعرف الوحيد:\u00A00884195R"  # Espace insécable
    )
    
    with open("document_espaces_insecables.txt", "w", encoding="utf-8") as f:
        f.write("=== DOCUMENT AVEC ESPACES INSÉCABLES ===\n\n")
        f.write(content_with_spaces)
    
    # Version simple ASCII pour l'identifiant
    content_ascii = content.replace(
        "المعرف الوحيد: 0884195R",
        "المعرف الوحيد: 0884195R"  # Identifiant en ASCII pur
    )
    
    with open("document_ascii_id.txt", "w", encoding="utf-8") as f:
        f.write("=== DOCUMENT AVEC ID ASCII ===\n\n")
        f.write(content_ascii)

def create_position_test_file():
    """Crée un fichier de test pour vérifier les positions"""
    
    test_content = """TEST DE POSITIONNEMENT DES CARACTÈRES

Identifiant avec chiffres seulement: 08841958
Identifiant avec R à la fin: 0884195R
Identifiant avec R forcé: ‎0884195R‏
Identifiant avec direction LTR: \u202D0884195R\u202C

Test avec texte arabe avant:
المعرف الوحيد: 08841958
المعرف الوحيد: 0884195R
المعرف الوحيد: ‎0884195R‏
المعرف الوحيد: \u202D0884195R\u202C

Test de caractères individuels:
0 8 8 4 1 9 5 R
٠ ٨ ٨ ٤ ١ ٩ ٥ R

Fin du test."""

    with open("test_positions.txt", "w", encoding="utf-8") as f:
        f.write(test_content)

def main():
    print("🔄 Création du document avec positionnement forcé...")
    
    content = create_document_with_forced_positioning()
    
    print("🔄 Sauvegarde avec différents encodages...")
    save_with_multiple_encodings(content)
    
    print("🔄 Création du fichier de test...")
    create_position_test_file()
    
    print("\n✅ Fichiers créés:")
    print("  📄 document_utf8.txt - Version UTF-8 standard")
    print("  📄 document_utf8_bom.txt - Version UTF-8 avec BOM")
    print("  📄 document_force_direction.txt - Direction forcée")
    print("  📄 document_espaces_insecables.txt - Espaces insécables")
    print("  📄 document_ascii_id.txt - Identifiant ASCII")
    print("  📄 test_positions.txt - Tests de positionnement")
    
    print("\n🎯 VÉRIFICATION:")
    print("Dans chaque fichier, l'identifiant devrait être: 0884195R")
    print("Le R doit apparaître à la fin, pas au début")
    
    print("\n💡 CONSEILS:")
    print("- Ouvrez les fichiers avec différents éditeurs (Notepad++, VS Code, etc.)")
    print("- Vérifiez le fichier test_positions.txt pour voir lequel fonctionne le mieux")
    print("- Si le problème persiste, c'est l'éditeur de texte qui inverse la direction")

if __name__ == "__main__":
    main()
