import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import re

# === CONFIGURATION ===
image_path = "SABEH22.5.1.png"  # nom de ton image
output_text_file = "texte_extrait.txt"  # nom du fichier de sortie
output_positioned_file = "texte_positionne.txt"  # fichier avec positions

# === Chemin vers Tesseract sur Windows ===
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def preprocess_image(image):
    """Améliore l'image pour une meilleure reconnaissance OCR"""
    # Conversion en niveaux de gris
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Débruitage
    denoised = cv2.medianBlur(gray, 3)

    # Amélioration du contraste
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(denoised)

    # Binarisation adaptative pour préserver les détails
    binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY, 11, 2)

    # Morphologie pour nettoyer le texte
    kernel = np.ones((1,1), np.uint8)
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    return cleaned

def extract_text_with_positions(image):
    """Extrait le texte en préservant les positions"""
    # Configuration OCR optimisée pour arabe + français + chiffres
    custom_config = r'--oem 3 --psm 6 -l ara+fra+eng'

    # Obtenir les données détaillées avec positions
    data = pytesseract.image_to_data(image, config=custom_config, output_type=pytesseract.Output.DICT)

    # Organiser le texte par lignes en préservant les positions
    lines = {}

    for i in range(len(data['text'])):
        if int(data['conf'][i]) > 20:  # Seuil de confiance plus bas
            text = data['text'][i].strip()
            if text:
                x = data['left'][i]
                y = data['top'][i]
                w = data['width'][i]
                h = data['height'][i]

                # Grouper par ligne avec une tolérance plus fine
                line_key = y // 15 * 15  # Grouper par tranches de 15 pixels

                if line_key not in lines:
                    lines[line_key] = []

                lines[line_key].append({
                    'text': text,
                    'x': x,
                    'y': y,
                    'w': w,
                    'h': h,
                    'conf': data['conf'][i]
                })

    return lines

def is_arabic_text(text):
    """Vérifie si le texte contient principalement de l'arabe"""
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.5

def reconstruct_text_layout(lines):
    """Reconstruit le texte en préservant la mise en page"""
    reconstructed_text = ""
    positioned_text = ""

    # Trier les lignes par position Y
    sorted_lines = sorted(lines.keys())

    for line_y in sorted_lines:
        # Séparer les mots arabes et non-arabes
        arabic_words = []
        other_words = []

        for word in lines[line_y]:
            if is_arabic_text(word['text']):
                arabic_words.append(word)
            else:
                other_words.append(word)

        # Trier les mots arabes de droite à gauche (X décroissant)
        arabic_words.sort(key=lambda x: x['x'], reverse=True)
        # Trier les autres mots de gauche à droite (X croissant)
        other_words.sort(key=lambda x: x['x'])

        # Combiner en gardant l'ordre spatial original
        all_words = sorted(lines[line_y], key=lambda x: x['x'])

        line_text = ""
        line_positions = f"Ligne Y={line_y}: "

        # Reconstruire la ligne en respectant les positions
        current_x = 0
        for word in all_words:
            # Ajouter des espaces pour maintenir l'espacement
            spaces_needed = max(0, (word['x'] - current_x) // 10)
            line_text += " " * spaces_needed + word['text']
            current_x = word['x'] + word['w']

            line_positions += f"[{word['text']}@({word['x']},{word['y']})] "

        reconstructed_text += line_text.strip() + "\n"
        positioned_text += line_positions + "\n"

    return reconstructed_text, positioned_text

def clean_and_correct_text(text):
    """Nettoie et corrige le texte extrait"""
    # Corrections spécifiques pour les erreurs communes
    corrections = {
        'u}2': '2',
        'Àl': 'A1',
        'القاي؛': 'التالي:',
        'دمة': 'دورة',
        'مكافنات': 'مكافآت',
        'إيراء': 'إجراء',
        'الوثانق': 'الوثائق',
        'جباس': 'جياس'
    }

    for wrong, correct in corrections.items():
        text = text.replace(wrong, correct)

    return text

# === ÉTAPE 1 : Charger et prétraiter l'image ===
print("🔄 Chargement et prétraitement de l'image...")
image = cv2.imread(image_path)
if image is None:
    print(f"❌ Erreur: Impossible de charger l'image {image_path}")
    exit(1)

processed_image = preprocess_image(image)

# === ÉTAPE 2 : Extraction avec préservation des positions ===
print("🔄 Extraction du texte avec positions...")
text_lines = extract_text_with_positions(processed_image)

# === ÉTAPE 3 : Reconstruction de la mise en page ===
print("🔄 Reconstruction de la mise en page...")
reconstructed_text, positioned_text = reconstruct_text_layout(text_lines)

# === ÉTAPE 4 : Nettoyage et correction ===
print("🔄 Nettoyage et correction du texte...")
final_text = clean_and_correct_text(reconstructed_text)

# === ÉTAPE 5 : Affichage et sauvegarde ===
print("📄 Texte extrait avec mise en page préservée :\n")
print("=" * 50)
print(final_text)
print("=" * 50)

# Sauvegarde du texte final
with open(output_text_file, "w", encoding="utf-8") as f:
    f.write(final_text)

# Sauvegarde avec informations de position
with open(output_positioned_file, "w", encoding="utf-8") as f:
    f.write("=== TEXTE AVEC POSITIONS ===\n\n")
    f.write(positioned_text)
    f.write("\n\n=== TEXTE FINAL CORRIGÉ ===\n\n")
    f.write(final_text)

print(f"\n✅ Texte enregistré dans : {output_text_file}")
print(f"✅ Texte avec positions dans : {output_positioned_file}")
print(f"📊 Nombre de lignes détectées : {len(text_lines)}")
