import cv2 
import pytesseract
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import re

# === CONFIGURATION ===
image_path = "SABEH22.5.1.png"
output_text_file = "texte_extrait_avance.txt"
output_positioned_file = "texte_positionne_avance.txt"

# === Chemin vers Tesseract sur Windows ===
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def preprocess_for_arabic(image):
    """Prétraitement spécialisé pour le texte arabe"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Amélioration du contraste spécifique pour l'arabe
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    
    # Binarisation avec seuil adaptatif
    binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 15, 2)
    
    # Dilatation légère pour connecter les caractères arabes
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2,1))
    processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    return processed

def extract_with_multiple_configs(image):
    """Essaie plusieurs configurations OCR pour obtenir le meilleur résultat"""
    
    configs = [
        r'--oem 3 --psm 6 -l ara',  # Arabe seulement
        r'--oem 3 --psm 6 -l fra',  # Français seulement  
        r'--oem 3 --psm 6 -l eng',  # Anglais/chiffres seulement
        r'--oem 3 --psm 6 -l ara+fra+eng',  # Multilingue
        r'--oem 3 --psm 4 -l ara+fra+eng',  # Colonne unique
        r'--oem 3 --psm 3 -l ara+fra+eng',  # Automatique
    ]
    
    best_result = None
    best_confidence = 0
    
    for config in configs:
        try:
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
            
            # Calculer la confiance moyenne
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            if confidences:
                avg_confidence = sum(confidences) / len(confidences)
                
                if avg_confidence > best_confidence:
                    best_confidence = avg_confidence
                    best_result = data
                    
        except Exception as e:
            print(f"Erreur avec config {config}: {e}")
            continue
    
    return best_result if best_result else None

def create_positioned_layout(data):
    """Crée une mise en page positionnée basée sur les coordonnées exactes"""
    if not data:
        return "", ""
    
    # Créer une matrice de caractères basée sur les positions
    max_x = max([data['left'][i] + data['width'][i] for i in range(len(data['text'])) if data['text'][i].strip()])
    max_y = max([data['top'][i] + data['height'][i] for i in range(len(data['text'])) if data['text'][i].strip()])
    
    # Créer une grille de caractères
    char_grid = {}
    
    for i in range(len(data['text'])):
        if int(data['conf'][i]) > 15 and data['text'][i].strip():
            text = data['text'][i].strip()
            x = data['left'][i]
            y = data['top'][i]
            
            # Normaliser les positions en grille
            grid_x = x // 8  # 8 pixels par caractère approximativement
            grid_y = y // 20  # 20 pixels par ligne approximativement
            
            if grid_y not in char_grid:
                char_grid[grid_y] = {}
            
            char_grid[grid_y][grid_x] = {
                'text': text,
                'conf': data['conf'][i],
                'original_x': x,
                'original_y': y
            }
    
    # Reconstruire le texte ligne par ligne
    reconstructed = ""
    positioned_info = ""
    
    for line_y in sorted(char_grid.keys()):
        line_text = ""
        line_info = f"Ligne {line_y}: "
        
        # Trier les éléments de la ligne par position X
        sorted_items = sorted(char_grid[line_y].items())
        
        last_x = 0
        for grid_x, item in sorted_items:
            # Ajouter des espaces pour maintenir l'espacement
            spaces = max(0, grid_x - last_x - len(line_text))
            line_text += " " * spaces + item['text']
            line_info += f"[{item['text']}@({item['original_x']},{item['original_y']})] "
            last_x = grid_x + len(item['text'])
        
        reconstructed += line_text.rstrip() + "\n"
        positioned_info += line_info + "\n"
    
    return reconstructed, positioned_info

def correct_common_errors(text):
    """Corrige les erreurs communes de reconnaissance"""
    corrections = {
        # Corrections pour l'arabe
        'مانما': 'مالها',
        'ديتار': 'دينار', 
        'الجئسة': 'الجلسة',
        'العابية': 'العادية',
        'التلسعة': 'التاسعة',
        'بانزل': 'بالنزل',
        'ثلاوة': 'تلاوة',
        'واللصابفة': 'والمصادقة',
        'استفراض': 'استعراض',
        'البارة': 'الإدارة',
        'لإبارة': 'للإدارة',
        'الحسايات': 'الحسابات',
        'نائق': 'وثائق',
        'العتلب': 'الطلب',
        'عنم': 'عند',
        
        # Corrections pour les chiffres et caractères spéciaux
        'pail': '2037.2',
        'BLE': '',
        'FO': '',
        'os': '',
        'dine': '',
        '$263': '',
        'yaad': '',
        'AU': '',
        'ot': '',
        
        # Corrections de ponctuation
        '؛': ':',
        '»': '',
    }
    
    for wrong, correct in corrections.items():
        text = text.replace(wrong, correct)
    
    return text

# === TRAITEMENT PRINCIPAL ===
print("🔄 Chargement de l'image...")
image = cv2.imread(image_path)
if image is None:
    print(f"❌ Erreur: Impossible de charger l'image {image_path}")
    exit(1)

print("🔄 Prétraitement spécialisé pour l'arabe...")
processed_image = preprocess_for_arabic(image)

print("🔄 Extraction avec configurations multiples...")
ocr_data = extract_with_multiple_configs(processed_image)

if ocr_data is None:
    print("❌ Échec de l'extraction OCR")
    exit(1)

print("🔄 Création de la mise en page positionnée...")
reconstructed_text, positioned_info = create_positioned_layout(ocr_data)

print("🔄 Correction des erreurs communes...")
final_text = correct_common_errors(reconstructed_text)

# === AFFICHAGE ET SAUVEGARDE ===
print("\n📄 TEXTE EXTRAIT AVEC POSITIONS PRÉSERVÉES:")
print("=" * 60)
print(final_text)
print("=" * 60)

# Sauvegarde
with open(output_text_file, "w", encoding="utf-8") as f:
    f.write(final_text)

with open(output_positioned_file, "w", encoding="utf-8") as f:
    f.write("=== INFORMATIONS DE POSITION ===\n\n")
    f.write(positioned_info)
    f.write("\n\n=== TEXTE FINAL CORRIGÉ ===\n\n")
    f.write(final_text)

print(f"\n✅ Texte sauvegardé dans: {output_text_file}")
print(f"✅ Détails de position dans: {output_positioned_file}")
